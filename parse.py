from openai import OpenAI

client = OpenAI(api_key="None", base_url="http://172.17.110.105:8000/v1")

text = """2024年成品油（燃料油）非国营贸易进口允许量申领条件、分配原则及相关程序
【发布单位】外贸司
【发布文号】商务部公告2023年第60号
【发文日期】2023年12月22日

根据《中华人民共和国货物进出口管理条例》和原外经贸部令2002年第27号（《原油、成品油、化肥国营贸易进口经营管理试行办法》），商务部制定了《2024年成品油（燃料油）非国营贸易进口允许量申领条件、分配原则及相关程序》，现予公布。
                                                         商务部
                                                     2023年12月22日



2024年成品油（燃料油）非国营贸易进口允许量
申领条件、分配原则及相关程序
第一条 成品油（燃料油）进口管理
成品油（燃料油）（以下简称燃料油）进口实行国营贸易管理，同时根据中国加入世界贸易组织议定书相关规定，允许一定数量的非国营贸易进口，由符合非国营贸易进口允许量申领条件的企业在年度进口允许量范围内进口。
第二条 燃料油非国营贸易进口允许量
2024年燃料油非国营贸易进口允许量（以下简称燃料油进口允许量）为2000万吨。
第三条 燃料油非国营贸易进口允许量申领条件
    （一）具有独立的法人资格；
　　（二）拥有不低于1万吨的燃料油进口码头或铁路专用线（仅限边疆陆运企业）等接卸设施所有权或使用权；
　　（三）拥有库容不低于5万立方米的燃料油储罐或油库所有权或使用权；
　　（四）国内银行授信额度不低于2000万美元或1.2亿元人民币；
　　（五）近两年无违反国家法律法规的行为，未发生出现人员重伤或死亡的安全生产事故；
　　（六）其他需要考虑的因素。





第四条 申请报送材料
（一）企业申请函，包括：公司基本情况、符合申请条件的说明；企业法人营业执照副本复印件（须加盖申请企业公章）；申请企业的海关编码、企业代码材料;
　　（二）拥有不低于1万吨码头或铁路专用线（仅限边疆陆运企业）的申请企业提供相关产权证明文件；不拥有码头或铁路专用线（仅限边疆陆运企业）的企业需提供码头或铁路专用线（仅限边疆陆运企业）等的使用权协议；
　　（三）拥有不低于5万立方米燃料油储罐或油库的申请企业提供相关产权证明文件；不拥有燃料油储罐或油库的企业需提供签订燃料油储罐或油库使用权协议；


（四）国内银行出具的授信证明文件；
（五）近两年无违反国家法律法规行为、未发生出现人员重伤或死亡的安全生产事故的企业承诺书。
以上申请材料当年有效。
第五条 审核和公示
新申请企业可根据本公告有关规定，向所在省、自治区、直辖市及计划单列市、新疆生产建设兵团商务主管部门（以下称省级商务主管部门）提交2024年燃料油非国营贸易进口允许量申请。
省级商务主管部门对企业申请材料进行初步审核，确保企业提交材料符合公告要求，并对企业合法合规经营和安全生产情况进行初步核查。有关审核情况在上报汇总申请材料中单独列明，并于2024年3月31日前报送商务部（报送地址：北京市东城区东长安街2号商务部行政事务服务大厅11号窗口；联系电话：010-65197961；邮政编码：100731）。封装申请材料的信封或者物流纸箱的表面需注明“事项编号：18010-001”字样。中央企业下属企业，由中央企业统筹审核并将申请及有关材料按上述时间报送商务部（寄送要求同上）。
商务部对企业申请进行审核，并在网站公示经审核符合条件的企业名单，公示期5个工作日。公示期间，对公示名单有异议的，可向商务部提请复核。公示通过的企业纳入已符合燃料油非国营贸易进口允许量申领条件的企业名单，可按程序申领燃料油进口允许量。
连续两年没有实际进口燃料油的企业，从符合非国营贸易进口允许量申领条件的企业名单中移除。商务部定期公布已符合燃料油非国营贸易进口允许量申领条件的企业名单（2024年名单附后）。2022年-2023年（截至10月）没有实际进口燃料油的企业，从本次名单中移除。
第六条 进口允许量先来先领
2024年燃料油进口允许量实行“先来先领”的分配方式。符合非国营贸易进口允许量申领条件的企业根据实际进口需求申领燃料油进口允许量，其可申领的起始数量根据2023年燃料油进口允许量完成情况、许可证核销率设定。在起始申领数量内企业可分次申领燃料油自动进口许可证。企业报关进口或将未使用完毕的自动进口许可证退回后，可在不超过起始数量的范围内再次申领自动进口许可证，直至燃料油进口允许量总量申领完毕。
第七条 2024年起始进口允许量
（一）2023年燃料油许可证核销率80%且起始允许量完成率80%以上的企业，2024年起始进口允许量上调10万吨；
（二）2023年燃料油许可证核销率50%-79%且起始允许量完成率50%以上的企业，2024年起始进口允许量上调5万吨；
（三）2023年燃料油许可证核销率25%以下的企业，2024年扣减50%的起始进口允许量；
（四）符合燃料油非国营贸易进口允许量申领条件的新企业，2024年起始进口允许量为5万吨；
（五）2024年燃料油起始进口允许量最高不超过30万吨，最低不低于5万吨。
第八条 燃料油自动进口许可证的申领
企业向商务部配额许可证事务局和相关省级发证机构申领燃料油自动进口许可证时须提供以下材料原件或副本：
（一）《自动进口许可证申请表》；
（二）具有法律效力的进口合同或委托代理的进口合同；
（三）相关发证机构要求出具的其他材料。
第九条 燃料油自动进口许可证的受理及发放
商务部配额许可证事务局和各地省级发证机构负责受理企业申领燃料油自动进口许可证，在申请材料齐全后5个工作日内为符合条件的企业签发自动进口许可证。
第十条 燃料油自动进口许可证有效期、更改和遗失
燃料油自动进口许可证自签发之日起3个月内有效，最迟不得超过2024年12月31日。需要延期或者变更的，需重新办理，旧证撤销后换发新证需在备注栏中注明原证号。自动进口许可证遗失，企业应在10个工作日内向原发证机构和原证所列报关口岸办理挂失手续。核实无误后，原发证机构签发新证并在备注栏中注明原证号。
第十一条 未使用燃料油自动进口许可证的退还
企业将未使用或未使用完毕的燃料油自动进口许可证在有效期满后10个工作日内退还原发证机构。企业退回的未使用允许量归入全国未使用燃料油允许量，供企业先来先领。
第十二条 未使用燃料油进口允许量的公布
燃料油自动进口许可证申领数量超过年度允许量90%时，商务部配额许可证事务局通过适当方式公布燃料油自动进口许可证剩余数量，方便企业做好进口业务安排。
第十三条 企业的相关责任
企业需对所报送燃料油非国营贸易资格备案和燃料油自动进口许可证的申请材料真实性负责，并出具加盖企业公章的承诺函。企业如有伪造、变造报送和申领材料行为的，将追究其法律责任。
伪造、变造或者买卖自动进口许可证的，将依法追究其法律责任。
对有上述违法行为的企业，两年内将不予受理其燃料油进口业务申请。
第十四条 其他
自2023年12月31日起，各发证机构受理燃料油自动进口许可证申请并发放2024年燃料油自动进口许可证。
第十五条 本公告由商务部负责解释。

"""

prompt = """# Role
You are a world-class Knowledge Graph Architect. Your task is to perform a complete schema induction from a given text. This involves identifying all entity types, their attributes, and the relations between them.

# Context
You must analyze it deeply to propose a comprehensive and well-structured knowledge graph schema.

# Instructions
1.  **Identify Entity Types:** First, identify all primary entity types (e.g., Company, Person, Product).
2.  **Identify Attributes:** For each entity type, determine its intrinsic properties (e.g., for Company: `name`, `stock_symbol`, `founding_date`). Suggest a data type for each attribute (e.g., String, Number, Date).
3.  **Identify Relation Types:** Then, identify the directed relationships that connect these entity types. A relation must have a subject entity type, a relation name, and an object entity type (e.g., `(Person) -[MANAGES]-> (Product)`).
4.  **Synthesize:** Combine all this information into a single, structured JSON output. Think carefully and ensure the schema is coherent and logical.

# Output Format
Please provide your final schema as a single, nested JSON object with three main keys: "entity_types", "attribute_details", and "relation_types".

{
  "entity_types": [
    {
      "name": "...",
      "description": "...",
      "attribute_name_1": "...",
      "attribute_name_2": "..."
    }
  ],
  "relation_types": [
    {
      "relation_name": "...",
      "subject_type": "...",
      "object_type": "...",
      "description": "..."
    }
  ]
}

# Text to Analyze"""

completion = client.chat.completions.create(
    model="Qwen2.5-72B-Instruct-GPTQ-Int4",
    messages=[
        {"role": "user", "content": prompt + "\n" + text},
    ],
    stream=False,
    temperature=0.2, # 确保输出确定性
)

llm_output = completion.choices[0].message.content

print(llm_output)