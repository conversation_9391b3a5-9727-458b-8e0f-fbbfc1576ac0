import json
import re
import uuid
import logging
import time # Add time import
from nebula3.gclient.net import ConnectionPool
from nebula3.Config import Config
from nebula3.gclient.net.Session import Session # Import Session for type hinting in comments
import traceback # Add traceback for detailed error logging
import openai
from openai import OpenAI # Import OpenAI client
from nebula3.common.ttypes import DataSet, Edge, HostAddr, Path, Point, Value, Vertex
from nebula3.data.ResultSet import ResultSet

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- 辅助函数 ---

def parse_schema_from_prompt(prompt_content):
    """
    从提示词内容中解析Schema定义。
    这里简化处理，直接硬编码Schema，因为提示词是固定的。
    在更复杂的场景下，需要通过正则表达式或NLP方法从文本中动态解析。
    """
    entity_types = {
        "PERSON": ["name", "title"],
        "ORGANIZATION": ["name", "founding_date", "stock_code"],
        "PRODUCT": ["name"],
        "LOCATION": ["name"],
        "DATE": ["name"]
    }
    relation_types = {
        "works_for": ("PERSON", "ORGANIZATION"),
        "founded_by": ("ORGANIZATION", "PERSON"),
        "located_in": ("ORGANIZATION", "LOCATION"),
        "product_of": ("PRODUCT", "ORGANIZATION")
    }
    return {"entity_types": entity_types, "relation_types": relation_types}

def escape_string(text):
    """Escapes double quotes in a string for nGQL."""
    if isinstance(text, str):
        return text.replace('"', '\"')
    return str(text)


def connect_to_nebulagraph(config):
    """
    连接到NebulaGraph。
    """
    logging.info(f"尝试连接到NebulaGraph: {config['host']}:{config['port']}, 空间: {config['space_name']}")
    
    config_ng = Config()
    config_ng.max_connection_pool_size = 10 # Example: set max connection pool size
    pool = ConnectionPool()
    addresses = [(config['host'], config['port'])]
    ok = pool.init(addresses, config_ng)
    if not ok:
        logging.error("NebulaGraph 连接池初始化失败。")
        raise Exception("NebulaGraph Connection Pool Init Failed")

    # 获取Session，并选择空间
    session: Session = None
    try:
        session = pool.get_session(config['username'], config['password'])
        if session:
            logging.info(f"成功连接到NebulaGraph: {config['host']}:{config['port']}")
            return session
        else:
            raise Exception("获取NebulaGraph会话失败。")
    except Exception as e:
        log_error(f"NebulaGraph 连接或会话获取失败: {e}", traceback.format_exc())
        if session:
            pool.close()
        raise

def close_nebulagraph_connection(session: Session):
    """
    关闭NebulaGraph连接池中的会话。
    """
    if session:
        logging.info("关闭NebulaGraph会话。")
        session.release()


def create_or_check_nebulagraph_schema(session: Session, schema_def, space_name):
    """
    在NebulaGraph中创建或检查Tag和Edge Type。
    """
    logging.info(f"准备创建或检查NebulaGraph Schema in space: {space_name}")

    # 创建或使用知识空间
    create_space_query = f"CREATE SPACE IF NOT EXISTS `{space_name}`(vid_type=FIXED_STRING(256));"
    logging.info(f"nGQL: {create_space_query}")
    resp = session.execute(create_space_query)
    if not resp.is_succeeded():
        log_error(f"创建空间 '{space_name}' 失败: {resp.error_msg()}", resp.error_msg())
        raise Exception(f"创建空间 '{space_name}' 失败: {resp.error_msg()}")

    logging.info(f"空间 '{space_name}' 创建/检查完成。等待空间同步...")
    time.sleep(10) # 留出时间等待空间同步

    use_space_query = f"USE `{space_name}`;"
    logging.info(f"nGQL: {use_space_query}")
    resp = session.execute(use_space_query)
    if not resp.is_succeeded():
        log_error(f"使用空间 '{space_name}' 失败: {resp.error_msg()}", resp.error_msg())
        raise Exception(f"使用空间 '{space_name}' 失败: {resp.error_msg()}")

    # 创建Tag
    for entity_type, attributes in schema_def["entity_types"].items():
        props = []
        for attr in attributes:
            props.append(f"`{attr}` string") # All attributes are strings for simplicity

        props_str = ", ".join(props)
        query = f"CREATE TAG IF NOT EXISTS `{entity_type}`({props_str});"
        logging.info(f"nGQL: {query}")
        resp = session.execute(query)
        if not resp.is_succeeded():
            log_error(f"创建Tag '{entity_type}' 失败: {resp.error_msg()}", resp.error_msg())
            raise Exception(f"创建Tag '{entity_type}' 失败: {resp.error_msg()}")

    # 创建Edge Type
    for relation_type in schema_def["relation_types"].keys():
        query = f"CREATE EDGE IF NOT EXISTS `{relation_type}`();"
        logging.info(f"nGQL: {query}")
        resp = session.execute(query)
        if not resp.is_succeeded():
            log_error(f"创建Edge Type '{relation_type}' 失败: {resp.error_msg()}", resp.error_msg())
            raise Exception(f"创建Edge Type '{relation_type}' 失败: {resp.error_msg()}")

    logging.info("NebulaGraph Schema创建/检查完成。等待Schema同步...")
    time.sleep(10) # 留出时间等待Schema同步

def chunk_document(content, max_tokens):
    """
    将长文档内容分割成更小的块，以适应LLM的token限制。
    简单的模拟，实际应根据LLM的token限制和文本结构进行分割
    """
    if len(content) > max_tokens * 4: # 假设平均一个汉字4个token
        logging.warning("文档内容过长，可能超出LLM的token限制。请实现更智能的分块策略。")
    return [content]

def get_prompt_template():
    """
    获取大模型提示词模板。
    """
    return """# 角色
你是一位顶级的知识图谱构建专家，擅长从非结构化文本中一次性提取所有实体、关系和属性。

# 任务
你的任务是分析下面的【源文本】，并严格按照我定义的【Schema】和【输出格式】提取信息。

# Schema 定义
1.  **实体类型 (Entity Types)**:
    - PERSON: 人名
    - ORGANIZATION: 组织机构名
    - PRODUCT: 产品名称
    - LOCATION: 地理位置
    - DATE: 日期

2.  **实体属性 (Attributes per Entity Type)**:
    - PERSON:
        - "title": 职位或头衔
    - ORGANIZATION:
        - "founding_date": 成立日期
        - "stock_code": 股票代码

3.  **关系类型 (Relation Types)**:
    - works_for (任职于): (PERSON, ORGANIZATION)
    - founded_by (创始人是): (ORGANIZATION, PERSON)
    - located_in (位于): (ORGANIZATION, LOCATION)
    - product_of (产品属于): (PRODUCT, ORGANIZATION)

# 思维链指引 (Chain of Thought)
请按照以下步骤思考和执行：
1.  通读【源文本】，识别出所有符合【实体类型】定义的实体。
2.  对于每个识别出的实体，再次阅读文本，找到并提取其对应的【实体属性】。
3.  分析实体之间的联系，识别出符合【关系类型】定义的关系三元组。
4.  最后，将所有提取的信息整合到指定的JSON结构中。

# 输出格式
请将最终结果组织成一个单一的JSON对象，该对象包含两个键："entities" 和 "relations"。
- "entities": 一个JSON数组，每个对象代表一个实体，包含 "text", "type", 和 "attributes"。
- "relations": 一个JSON数组，每个对象代表一个关系三元组，包含 "head", "relation", 和 "tail"，"head"和"tail"对应实体列表中的"text"。

# 示例
【源文本】: "雷军在北京创立了小米集团，并发布了小米手机。"
【输出】:
{{
  "entities": [
    {{
      "text": "雷军",
      "type": "PERSON",
      "attributes": {{}}
    }},
    {{
      "text": "北京",
      "type": "LOCATION",
      "attributes": {{}}
    }},
    {{
      "text": "小米集团",
      "type": "ORGANIZATION",
      "attributes": {{}}
    }},
    {{
      "text": "小米手机",
      "type": "PRODUCT",
      "attributes": {{}}
    }}
  ],
  "relations": [
    {{
      "head": "小米集团",
      "relation": "founded_by",
      "tail": "雷军"
    }},
    {{
      "head": "小米集团",
      "relation": "located_in",
      "tail": "北京"
    }},
    {{
      "head": "小米手机",
      "relation": "product_of",
      "tail": "小米集团"
    }}
  ]
}}

# 开始处理
【源文本】:
{content}

【输出】:
"""


def call_llm_api(endpoint, api_key, prompt, model_name="Qwen2.5-72B-Instruct-GPTQ-Int4"):
    """
    调用大模型API进行知识抽取。
    """
    logging.info(f"调用LLM API: {endpoint}, 模型: {model_name}")
    logging.debug(f"LLM Prompt:\n{prompt[:200]}...") # 打印部分prompt

    try:
        client = OpenAI(api_key=api_key, base_url=endpoint)

        completion = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "user", "content": prompt},
            ],
            stream=False,
            temperature=0.2, # 确保输出确定性
        )
        
        llm_output = completion.choices[0].message.content
        logging.debug(f"LLM Raw Output:\n{llm_output[:200]}...")
        
        # 尝试从LLM输出中提取JSON部分
        # 假设JSON总是包含在 ```json ... ``` 块中
        json_match = re.search(r'```json\n(.*?)```', llm_output, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            return json.loads(json_str)
        else:
            # 如果没有找到```json```块，尝试直接解析整个输出为JSON
            logging.warning("LLM响应中未找到JSON代码块，尝试直接解析整个响应。")
            return json.loads(llm_output)

    except json.JSONDecodeError as e:
        log_error(f"LLM响应JSON解析失败: {e}", f"Response: {llm_output}, Traceback: {traceback.format_exc()}")
        return {"entities": [], "relations": []}
    except Exception as e:
        log_error(f"调用LLM API失败: {e}", traceback.format_exc())
        return {"entities": [], "relations": []}

def parse_llm_output_json(json_response):
    """
    解析大模型返回的JSON，提取entities和relations。
    """
    try:
        return json_response
    except json.JSONDecodeError as e:
        logging.error(f"LLM响应JSON解析失败: {e}, 响应内容: {json_response}")
        return {"entities": [], "relations": []}

def normalize_extracted_data(parsed_data, entity_name_to_vid_map):
    """
    规范化实体和关系，生成或查找VID，并更新全局映射。
    global_llm_id_to_vid_map: 存储 {chunk_id_llm_id: vid}，用于关系查找
    entity_name_to_vid_map: 存储 {entity_type_entity_name: vid}，用于实体去重和跨块ID统一
    """
    normalized_entities = []
    normalized_relations = []
    
    entities = parsed_data.get("entities", [])
    relations = parsed_data.get("relations", [])

    current_chunk_id_map = {} # 存储当前块LLM id到VID的映射

    for entity in entities:
        # LLM返回的JSON中，实体的ID是文本，而不是一个单独的"id"字段
        # 这里的LLM ID应该就是实体文本，或者是一个内部用于LLM生成关系的临时ID
        # 根据最新的提示词，LLM返回的实体中没有"id"字段，只有"text", "type", "attributes"
        # 所以LLM ID的概念需要重新审视。对于关系，"head"和"tail"直接是实体文本
        text = entity["text"]
        entity_type = entity["type"]
        attributes = entity.get("attributes", {})

        # 构建一个基于类型和文本的唯一键，用于实体去重
        unique_entity_key = f"{entity_type}:{text}"
        
        vid = None
        if unique_entity_key in entity_name_to_vid_map:
            # 实体已存在，使用已有的VID
            vid = entity_name_to_vid_map[unique_entity_key]
            logging.debug(f"实体 '{text}' ({entity_type}) 已存在，使用 VID: {vid}")
        else:
            # 新实体，生成新的VID
            # 推荐使用 entity_type:entity_text 作为 VID，确保唯一性和可读性
            # 或者使用 UUID.uuid4().hex
            vid = f"{entity_type}:{text}" if len(f"{entity_type}:{text}") <= 256 else uuid.uuid4().hex
            entity_name_to_vid_map[unique_entity_key] = vid
            logging.debug(f"生成新实体 VID: {vid} for '{text}' ({entity_type})")

            normalized_entities.append({
                "vid": vid,
                "text": text,
                "type": entity_type,
                "attributes": attributes
            })
        
        # 由于LLM不再返回"id"字段，current_chunk_id_map不再基于llm_id，而是基于entity_text
        # 但normalize_extracted_data的目的是为了规范化，而不是为了临时映射
        # 关系中的"head"和"tail"直接是实体文本，因此在处理关系时，直接根据文本查找VID即可
        # global_llm_id_to_vid_map 保持为空，因为不再需要LLM的临时ID到VID的映射

    for relation in relations:
        head_text = relation["head"]
        tail_text = relation["tail"]
        relation_type = relation["relation"]

        # 查找head和tail的VID
        head_vid = None
        tail_vid = None
        
        # 优先在当前批次（normalized_entities）中查找，确保引用到新创建的实体
        for entity in normalized_entities:
            if entity["text"] == head_text:
                head_vid = entity["vid"]
            if entity["text"] == tail_text:
                tail_vid = entity["vid"]
            if head_vid and tail_vid: # 找到两者，提前退出
                break

        # 如果在当前批次中未找到，则在全局映射（entity_name_to_vid_map）中查找
        # 这种情况下需要知道实体的类型才能构建正确的unique_entity_key
        # 这是一个复杂的问题，因为关系本身不带实体类型信息
        # 简化处理：遍历entity_name_to_vid_map，只要文本匹配就取第一个
        # 实际生产中应避免这种模糊匹配，LLM应返回实体ID或包含实体类型
        if not head_vid:
            for unique_key, vid_val in entity_name_to_vid_map.items():
                # unique_key 格式为 "ENTITY_TYPE:ENTITY_TEXT"
                if unique_key.endswith(f":{head_text}"):
                    head_vid = vid_val
                    break
        if not tail_vid:
            for unique_key, vid_val in entity_name_to_vid_map.items():
                if unique_key.endswith(f":{tail_text}"):
                    tail_vid = vid_val
                    break

        if head_vid and tail_vid:
            normalized_relations.append({
                "head_vid": head_vid,
                "relation_type": relation_type,
                "tail_vid": tail_vid
            })
        else:
            logging.warning(f"无法为关系 ({head_text})--[{relation_type}]-->({tail_text}) 找到对应的VID。跳过此关系。")
            
    return normalized_entities, normalized_relations

def remove_duplicate_entities(entities_list):
    """
    根据VID去重实体。
    """
    unique_entities = {}
    for entity in entities_list:
        unique_entities[entity["vid"]] = entity
    return list(unique_entities.values())

def remove_duplicate_relations(relations_list):
    """
    根据头尾VID和关系类型去重关系。
    """
    unique_relations = set()
    result_relations = []
    for rel in relations_list:
        # 创建一个可哈希的元组作为唯一标识
        rel_key = (rel["head_vid"], rel["relation_type"], rel["tail_vid"])
        if rel_key not in unique_relations:
            unique_relations.add(rel_key)
            result_relations.append(rel)
    return result_relations

def batch_insert_vertices(session: Session, entities, schema_def):
    """
    批量插入实体（节点）的nGQL语句。
    """
    if not entities:
        return

    # 按实体类型分组，便于构建批量插入语句
    grouped_entities = {}
    for entity in entities:
        entity_type = entity["type"]
        if entity_type not in grouped_entities:
            grouped_entities[entity_type] = []
        grouped_entities[entity_type].append(entity)

    for entity_type, entity_list in grouped_entities.items():
        # 获取该实体类型的所有属性名 (这里根据动态Schema获取)
        props = schema_def["entity_types"].get(entity_type, [])
        
        prop_keys_str = ", ".join([f"`{p}`" for p in props])
        values_str_parts = []
        for entity in entity_list:
            vid = escape_string(entity["vid"])
            text = escape_string(entity["text"])
            attrs = entity["attributes"]

            # 构建属性值字符串，根据动态Schema
            attr_values = []
            for prop in props:
                if prop == "name":
                    attr_values.append(f'"{text}"') # 'name' attribute is always the entity's text
                else:
                    attr_values.append(f'"{escape_string(attrs.get(prop, ""))}"')
            
            values_str_parts.append(f'"{vid}":({", ".join(attr_values)})')
        
        if values_str_parts:
            values_clause = ", ".join(values_str_parts)
            query = f"INSERT VERTEX `{entity_type}`({prop_keys_str}) VALUES {values_clause};"
            logging.info(f"nGQL: {query[:200]}...") # Log truncated query
            resp = session.execute(query)
            if not resp.is_succeeded():
                log_error(f"批量插入Tag '{entity_type}' 失败: {resp.error_msg()}", resp.error_msg())
                raise Exception(f"批量插入Tag '{entity_type}' 失败: {resp.error_msg()}")

    logging.info("批量实体插入nGQL完成。")

def batch_insert_edges(session: Session, relations):
    """
    批量插入关系（边）的nGQL语句。
    """
    if not relations:
        return
    
    # 按关系类型分组
    grouped_relations = {}
    for rel in relations:
        rel_type = rel["relation_type"]
        if rel_type not in grouped_relations:
            grouped_relations[rel_type] = []
        grouped_relations[rel_type].append(rel)

    for rel_type, rel_list in grouped_relations.items():
        values_str_parts = []
        for rel in rel_list:
            head_vid = escape_string(rel["head_vid"])
            tail_vid = escape_string(rel["tail_vid"])
            values_str_parts.append(f'"{head_vid}"->"{tail_vid}":()')
        
        if values_str_parts:
            values_clause = ", ".join(values_str_parts)
            query = f"INSERT EDGE `{rel_type}`() VALUES {values_clause};"
            logging.info(f"nGQL: {query[:200]}...") # Log truncated query
            resp = session.execute(query)
            if not resp.is_succeeded():
                log_error(f"批量插入Edge '{rel_type}' 失败: {resp.error_msg()}", resp.error_msg())
                raise Exception(f"批量插入Edge '{rel_type}' 失败: {resp.error_msg()}")
            
    logging.info("批量关系插入nGQL完成。")

def log_success(message):
    logging.info(message)

def log_error(message, details):
    logging.error(f"{message}\nDetails:\n{details}")

# --- 主算法实现 ---

def intelligent_kg_extraction_and_storage(document_content, llm_api_endpoint, llm_api_key, nebulagraph_config):
    """
    智能知识图谱抽取与存储算法。
    """
    nebulagraph_session = None
    try:
        logging.info("---------- 算法开始 ----------")
        prompt_template_content = get_prompt_template()

        # 步骤 1: 环境与Schema准备
        schema_def = parse_schema_from_prompt(prompt_template_content)
        nebulagraph_session = connect_to_nebulagraph(nebulagraph_config)
        create_or_check_nebulagraph_schema(nebulagraph_session, schema_def, nebulagraph_config['space_name'])

        # 存储所有抽取到的实体和关系，用于批量插入
        all_extracted_entities = []
        all_extracted_relations = []
        # 用于实体去重和跨块ID统一的映射
        entity_name_to_vid_map = {} 

        # 步骤 2: 文档分块与提示词构建
        # 简化处理，直接将整个文档作为一个chunk
        text_chunks = chunk_document(document_content, max_tokens=2000) # 假设LLM最大处理2000 token

        for chunk_id, chunk in enumerate(text_chunks):
            logging.info(f"处理文档块 {chunk_id + 1}/{len(text_chunks)}...")
            current_prompt = prompt_template_content.format(content=chunk)

            # 步骤 3: 大模型知识抽取
            llm_response_json = call_llm_api(llm_api_endpoint, llm_api_key, current_prompt)

            # 步骤 4: 知识解析与规范化
            parsed_data = parse_llm_output_json(llm_response_json)
            
            # 由于LLM不再返回"id"字段，global_llm_id_to_vid_map 不再需要
            normalized_entities, normalized_relations = normalize_extracted_data(
                parsed_data, entity_name_to_vid_map
            )

            all_extracted_entities.extend(normalized_entities)
            all_extracted_relations.extend(normalized_relations)

        # 步骤 5: NebulaGraph数据存储
        # 进行最终的实体和关系去重
        unique_entities = remove_duplicate_entities(all_extracted_entities)
        unique_relations = remove_duplicate_relations(all_extracted_relations)

        # 批量插入顶点（实体）
        batch_insert_vertices(nebulagraph_session, unique_entities, schema_def)
        
        # 批量插入边（关系）
        batch_insert_edges(nebulagraph_session, unique_relations)

        log_success("知识图谱抽取与存储成功完成。")
        logging.info("---------- 算法结束 ----------")
        return True

    except Exception as e:
        # 步骤 6: 错误处理与日志
        log_error(f"知识图谱抽取与存储失败: {e}", traceback.format_exc())
        logging.info("---------- 算法异常终止 ----------")
        return False
    finally:
        if nebulagraph_session:
            close_nebulagraph_connection(nebulagraph_session)


# --- 示例用法 ---
if __name__ == "__main__":
    # 示例文档内容1
    document_content_1 = "雷军在北京创立了小米集团，并发布了小米手机。"

    # 示例文档内容2 (将触发模拟LLM的另一个分支)
    document_content_2 = "李明在腾讯工作，职位是高级工程师。腾讯成立于1998年11月11日，股票代码是0700.HK。此外，王芳也是腾讯的一名员工。"
    
    # 示例文档内容3 (结合多个模拟LLM分支)
    document_content_3 = """
2024年成品油（燃料油）非国营贸易进口允许量申领条件、分配原则及相关程序
【发布单位】外贸司
【发布文号】商务部公告2023年第60号
【发文日期】2023年12月22日

根据《中华人民共和国货物进出口管理条例》和原外经贸部令2002年第27号（《原油、成品油、化肥国营贸易进口经营管理试行办法》），商务部制定了《2024年成品油（燃料油）非国营贸易进口允许量申领条件、分配原则及相关程序》，现予公布。
                                                         商务部
                                                     2023年12月22日



2024年成品油（燃料油）非国营贸易进口允许量
申领条件、分配原则及相关程序
第一条 成品油（燃料油）进口管理
成品油（燃料油）（以下简称燃料油）进口实行国营贸易管理，同时根据中国加入世界贸易组织议定书相关规定，允许一定数量的非国营贸易进口，由符合非国营贸易进口允许量申领条件的企业在年度进口允许量范围内进口。
第二条 燃料油非国营贸易进口允许量
2024年燃料油非国营贸易进口允许量（以下简称燃料油进口允许量）为2000万吨。
第三条 燃料油非国营贸易进口允许量申领条件
    （一）具有独立的法人资格；
　　（二）拥有不低于1万吨的燃料油进口码头或铁路专用线（仅限边疆陆运企业）等接卸设施所有权或使用权；
　　（三）拥有库容不低于5万立方米的燃料油储罐或油库所有权或使用权；
　　（四）国内银行授信额度不低于2000万美元或1.2亿元人民币；
　　（五）近两年无违反国家法律法规的行为，未发生出现人员重伤或死亡的安全生产事故；
　　（六）其他需要考虑的因素。





第四条 申请报送材料
（一）企业申请函，包括：公司基本情况、符合申请条件的说明；企业法人营业执照副本复印件（须加盖申请企业公章）；申请企业的海关编码、企业代码材料;
　　（二）拥有不低于1万吨码头或铁路专用线（仅限边疆陆运企业）的申请企业提供相关产权证明文件；不拥有码头或铁路专用线（仅限边疆陆运企业）的企业需提供码头或铁路专用线（仅限边疆陆运企业）等的使用权协议；
　　（三）拥有不低于5万立方米燃料油储罐或油库的申请企业提供相关产权证明文件；不拥有燃料油储罐或油库的企业需提供签订燃料油储罐或油库使用权协议；


（四）国内银行出具的授信证明文件；
（五）近两年无违反国家法律法规行为、未发生出现人员重伤或死亡的安全生产事故的企业承诺书。
以上申请材料当年有效。
第五条 审核和公示
新申请企业可根据本公告有关规定，向所在省、自治区、直辖市及计划单列市、新疆生产建设兵团商务主管部门（以下称省级商务主管部门）提交2024年燃料油非国营贸易进口允许量申请。
省级商务主管部门对企业申请材料进行初步审核，确保企业提交材料符合公告要求，并对企业合法合规经营和安全生产情况进行初步核查。有关审核情况在上报汇总申请材料中单独列明，并于2024年3月31日前报送商务部（报送地址：北京市东城区东长安街2号商务部行政事务服务大厅11号窗口；联系电话：010-65197961；邮政编码：100731）。封装申请材料的信封或者物流纸箱的表面需注明“事项编号：18010-001”字样。中央企业下属企业，由中央企业统筹审核并将申请及有关材料按上述时间报送商务部（寄送要求同上）。
商务部对企业申请进行审核，并在网站公示经审核符合条件的企业名单，公示期5个工作日。公示期间，对公示名单有异议的，可向商务部提请复核。公示通过的企业纳入已符合燃料油非国营贸易进口允许量申领条件的企业名单，可按程序申领燃料油进口允许量。
连续两年没有实际进口燃料油的企业，从符合非国营贸易进口允许量申领条件的企业名单中移除。商务部定期公布已符合燃料油非国营贸易进口允许量申领条件的企业名单（2024年名单附后）。2022年-2023年（截至10月）没有实际进口燃料油的企业，从本次名单中移除。
第六条 进口允许量先来先领
2024年燃料油进口允许量实行“先来先领”的分配方式。符合非国营贸易进口允许量申领条件的企业根据实际进口需求申领燃料油进口允许量，其可申领的起始数量根据2023年燃料油进口允许量完成情况、许可证核销率设定。在起始申领数量内企业可分次申领燃料油自动进口许可证。企业报关进口或将未使用完毕的自动进口许可证退回后，可在不超过起始数量的范围内再次申领自动进口许可证，直至燃料油进口允许量总量申领完毕。
第七条 2024年起始进口允许量
（一）2023年燃料油许可证核销率80%且起始允许量完成率80%以上的企业，2024年起始进口允许量上调10万吨；
（二）2023年燃料油许可证核销率50%-79%且起始允许量完成率50%以上的企业，2024年起始进口允许量上调5万吨；
（三）2023年燃料油许可证核销率25%以下的企业，2024年扣减50%的起始进口允许量；
（四）符合燃料油非国营贸易进口允许量申领条件的新企业，2024年起始进口允许量为5万吨；
（五）2024年燃料油起始进口允许量最高不超过30万吨，最低不低于5万吨。
第八条 燃料油自动进口许可证的申领
企业向商务部配额许可证事务局和相关省级发证机构申领燃料油自动进口许可证时须提供以下材料原件或副本：
（一）《自动进口许可证申请表》；
（二）具有法律效力的进口合同或委托代理的进口合同；
（三）相关发证机构要求出具的其他材料。
第九条 燃料油自动进口许可证的受理及发放
商务部配额许可证事务局和各地省级发证机构负责受理企业申领燃料油自动进口许可证，在申请材料齐全后5个工作日内为符合条件的企业签发自动进口许可证。
第十条 燃料油自动进口许可证有效期、更改和遗失
燃料油自动进口许可证自签发之日起3个月内有效，最迟不得超过2024年12月31日。需要延期或者变更的，需重新办理，旧证撤销后换发新证需在备注栏中注明原证号。自动进口许可证遗失，企业应在10个工作日内向原发证机构和原证所列报关口岸办理挂失手续。核实无误后，原发证机构签发新证并在备注栏中注明原证号。
第十一条 未使用燃料油自动进口许可证的退还
企业将未使用或未使用完毕的燃料油自动进口许可证在有效期满后10个工作日内退还原发证机构。企业退回的未使用允许量归入全国未使用燃料油允许量，供企业先来先领。
第十二条 未使用燃料油进口允许量的公布
燃料油自动进口许可证申领数量超过年度允许量90%时，商务部配额许可证事务局通过适当方式公布燃料油自动进口许可证剩余数量，方便企业做好进口业务安排。
第十三条 企业的相关责任
企业需对所报送燃料油非国营贸易资格备案和燃料油自动进口许可证的申请材料真实性负责，并出具加盖企业公章的承诺函。企业如有伪造、变造报送和申领材料行为的，将追究其法律责任。
伪造、变造或者买卖自动进口许可证的，将依法追究其法律责任。
对有上述违法行为的企业，两年内将不予受理其燃料油进口业务申请。
第十四条 其他
自2023年12月31日起，各发证机构受理燃料油自动进口许可证申请并发放2024年燃料油自动进口许可证。
第十五条 本公告由商务部负责解释。
    """

    llm_endpoint = "http://**************:8000/v1"
    llm_key = "123456"

    nebulagraph_config = {
        "host": "*************",
        "port": 9669,
        "username": "root",
        "password": "123456",
        "space_name": "kg_test"
    }

    print("\n--- 运行示例 3 ---")
    intelligent_kg_extraction_and_storage(
        document_content_3,
        llm_endpoint,
        llm_key,
        nebulagraph_config
    )